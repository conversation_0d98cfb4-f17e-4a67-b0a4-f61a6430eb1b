package downloadindex

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/cfg"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/engine"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/process/common"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"dip-agent/pkg/util/oss"
	"fmt"
	"github.com/pkg/errors"
	"path/filepath"
	"sort"
	"strings"
)

const (
	TypeName = "batch-check-index"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ResultMessageInfo struct {
	DiskUsageInfo   string `json:"diskUsageInfo"`
	MemoryUsageInfo string `json:"memoryUsageInfo"`
}

type ProcessRequest struct {
	Tasks            []*dto.UpdateIndexInfo `json:"tasks,omitempty" validate:"required,dive"`
	IsReboot         *bool                  `json:"isReboot,omitempty" default:"false"`         // 是否重启更新
	IsPartialSuccess *bool                  `json:"isPartialSuccess,omitempty" default:"false"` // 是否允许部分失败
}

type ProcessIndexInfo struct {
	*dto.UpdateIndexInfo
	buildResult *engine.BuildResult // 构建结果
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.ParamFail[string](err)
	}
	log.Info("[%s]check index from oss request: %+v", ctx.RequestId, pr)

	firstTask := pr.Tasks[0]
	c, err := oss.NewClient(firstTask.Oss.Endpoint, firstTask.Oss.Bucket, firstTask.Oss.AccessKey, firstTask.Oss.SecretKey)
	if err != nil {
		err = errors.WithMessage(err, "create oss client fail")
		return api.UpdateFail[string](err)
	}

	resultMessageInfo, err := RunTask(ctx.RequestId, ctx.TaskId, ctx.Carrier, c, pr)
	if err != nil {
		err = errors.WithMessage(err, "check index fail")
		return api.UpdateFail[string](err)
	}

	// 响应消息
	var resultMessage string
	resultMessageContent, err := json.Marshal(resultMessageInfo)
	if err != nil {
		log.Warn("[%s]marshal result message info fail: %+v", ctx.RequestId, err)
	} else {
		resultMessage = string(resultMessageContent)
	}
	return api.OfSuccessWithMessage[string]("check index success", resultMessage)
}

func RunTask(requestId, taskId string, carrier cfg.CommonCfg, c *oss.Client, pr *ProcessRequest) (*ResultMessageInfo, error) {
	var (
		err               error
		resultMessageInfo = &ResultMessageInfo{}
	)

	// 卡点校验: 当前任务是否属于当前集群
	err = common.CheckTask(requestId, taskId)
	if err != nil {
		return nil, err
	}

	// 卡点校验: 校验拉取的数据量是否符合最新的分区数据量
	// TODO 批量更新暂时不做dump校验
	//for _, task := range tasks {
	//	if *task.NeedDumpCheck {
	//		err = CheckDump(requestId, task, c, resultMessageInfo)
	//		if err != nil {
	//			return nil, err
	//		}
	//	}
	//}

	// 待处理索引包装
	processIndexTasks := make([]*ProcessIndexInfo, 0, len(pr.Tasks))
	for _, task := range pr.Tasks {
		processIndexTasks = append(processIndexTasks, &ProcessIndexInfo{
			UpdateIndexInfo: task,
		})
	}

	// 是否允许部分失败
	isPartialSuccess := pr.IsPartialSuccess != nil && *pr.IsPartialSuccess

	// 卡点校验: 判断磁盘和内存能否承载索引
	validTasks, err := checkIndexSize(requestId, isPartialSuccess, processIndexTasks, c, resultMessageInfo)
	if err != nil {
		return nil, err
	}

	// 重置pr.Tasks
	if len(validTasks) != len(pr.Tasks) {
		carrier.Put("tasks", pr.Tasks)
	}

	return resultMessageInfo, nil
}

// 读取oss上的check.json,获取索引的大小.判断磁盘和内存能否承载
func checkIndexSize(requestId string, isPartialSuccess bool, tasks []*ProcessIndexInfo, c *oss.Client, resultMessageInfo *ResultMessageInfo) ([]*ProcessIndexInfo, error) {
	// 批量读取oss上的check.json内容
	err := readOssCheckJsonConcurrent(requestId, tasks, c)
	if err != nil {
		return nil, err
	}

	// 将tasks根据索引大小排序, 从小到大
	sort.Slice(tasks, func(i, j int) bool {
		return tasks[i].buildResult.IndexTotalBytes < tasks[j].buildResult.IndexTotalBytes
	})

	// 判断磁盘能否承载
	err = checkDisk(requestId, isPartialSuccess, tasks, resultMessageInfo)
	if err != nil {
		return nil, err
	}

	// 过滤任务已经不能更新的
	afterDiskCheckTask := make([]*ProcessIndexInfo, 0)
	for _, task := range tasks {
		if task.CanUpdate {
			afterDiskCheckTask = append(afterDiskCheckTask, task)
		}
	}

	// 判断内存能否承载
	err = checkMemory(requestId, isPartialSuccess, afterDiskCheckTask, resultMessageInfo)
	if err != nil {
		return nil, err
	}

	afterMemoryCheckTask := make([]*ProcessIndexInfo, 0)
	for _, task := range afterDiskCheckTask {
		if task.CanUpdate {
			afterMemoryCheckTask = append(afterMemoryCheckTask, task)
		}
	}
	log.Info("[%s]check index memory size success, can update index count: %d/%d", requestId, len(afterMemoryCheckTask), len(tasks))

	return afterMemoryCheckTask, nil
}

// 并发读取oss上的check.json,获取索引的大小.判断磁盘和内存能否承载
func readOssCheckJsonConcurrent(requestId string, tasks []*ProcessIndexInfo, c *oss.Client) error {
	taskMap := make(map[string]*ProcessIndexInfo, len(tasks))
	asyncTasks := make([]util.Task[*engine.BuildResult], 0, len(tasks))
	for _, task := range tasks {
		t := task
		// 异步任务
		asyncTasks = append(asyncTasks, func() (*engine.BuildResult, error) {
			return readOssCheckJson(t, c)
		})

		// 任务map
		taskMap[t.IndexName] = t
	}
	results, err := util.ExecuteTasks(requestId, asyncTasks)
	if err != nil {
		return err
	}
	for _, result := range results {
		task := taskMap[result.IndexName]
		task.buildResult = result
	}
	return nil
}

func readOssCheckJson(pr *ProcessIndexInfo, c *oss.Client) (*engine.BuildResult, error) {
	// 读取oss上的check.json
	checkFilePath := filepath.Join(pr.Oss.Dir, pr.EngineBuildResultFileName)
	checkContent, err := c.ReadFileContent(checkFilePath)
	if err != nil {
		return nil, errors.WithMessagef(err, "read oss check.json file fail. oss path: %s", checkFilePath)
	}
	// 反序列化
	ber := &engine.BuildEngineResponse{}
	err = json.Unmarshal(checkContent, ber)
	if err != nil {
		return nil, errors.Wrapf(err, "unmarshal oss check.json file fail: %s", checkContent)
	}
	// 检查结果的响应头
	if ber.Header == nil {
		return nil, errors.New("invalid check.json file, header is nil")
	}
	// 检查结果是否成功
	if !ber.Header.IsSuccess() {
		return nil, errors.Errorf("check index size fail, build result fail: %s", ber.Header.ErrMsg())
	}
	// 获取构建结果
	buildResult := ber.BuildResult
	if buildResult == nil {
		return nil, errors.New("invalid check.json file, build result is nil")
	}
	buildResult.IndexName = pr.IndexName
	buildResult.IndexVersion = pr.IndexVersion
	return buildResult, nil
}

func checkMemory(requestId string, isPartialSuccess bool, tasks []*ProcessIndexInfo, resultMessageInfo *ResultMessageInfo) error {
	firstTask := tasks[0]
	// 获取当前已经使用内存大小
	memLimit, memUsed, err := common.GetMemoryUsage(requestId, firstTask.DbPath)
	if err != nil {
		return errors.WithMessage(err, "get memory usage fail")
	}

	// 获取rss大小
	rssMemory := util.GetRSSMemory()

	// 判断内存加上索引的大小使用是否会超过阈值
	var (
		afterIndexMemoryUsage     = int64(memUsed)
		afterIndexMemoryUsageRate float64
		memoryUsageInfo           strings.Builder
	)
	memoryUsageInfo.WriteString(fmt.Sprintf("(memoryUsedSize[%s]", util.ByteToHumanReadable(int64(memUsed))))
	for i, task := range tasks {
		// 待加载的索引大小
		indexBytes := task.buildResult.IndexTotalBytes
		// 计算索引加载到内存需要的大小: 索引加载到内存会膨胀约5%的大小
		indexMemorySize := indexBytes + indexBytes*int64(task.MemoryInflateFactor)/100

		// 获取原有索引的大小
		indexDbDir := filepath.Join(task.DbPath, task.IndexName)
		originalIndexSize, err := common.GetLocalRemoveIndexSize(requestId, indexDbDir, task.EngineBuildResultFileName, *task.IsDoubleVersion, task.ServingVersions)
		if err != nil {
			return errors.WithMessage(err, "get local index size fail")
		}

		// 判断内存加上索引的大小使用是否会超过阈值
		// 批量更新都是走重启更新，因此计算内存的时候需要减去原有索引的大小
		tmpAfterIndexMemoryUsageRate := float64(afterIndexMemoryUsage+indexMemorySize-originalIndexSize+rssMemory) * 100 / float64(memLimit)
		if tmpAfterIndexMemoryUsageRate > float64(task.MemoryUsageThreshold) {
			// 将当前以及其后的任务都设置为不能更新
			for j := i; j < len(tasks); j++ {
				curTask := tasks[j]
				curTask.CanUpdate = false
				curTask.Reason = "memory space not enough"
				log.Warn("[%s]index can not update: %s. reason: %s", requestId, curTask.IndexName, curTask.Reason)
			}

			// 首个索引就超过阈值, 需要记录到info中
			if i == 0 || !isPartialSuccess {
				//return errors.Errorf("memory space not enough!!! %s", memoryUsageInfo.String())
				afterIndexMemoryUsage += indexMemorySize - originalIndexSize
				memoryUsageInfo.WriteString(fmt.Sprintf(" + (%s[%s]", task.IndexName, util.ByteToHumanReadable(indexMemorySize)))
				memoryUsageInfo.WriteString(fmt.Sprintf(" - original[%s][%s])", task.IndexName, util.ByteToHumanReadable(originalIndexSize)))
			}
			break
		}

		// 内存足够
		afterIndexMemoryUsage += indexMemorySize - originalIndexSize
		memoryUsageInfo.WriteString(fmt.Sprintf(" + (%s[%s]", task.IndexName, util.ByteToHumanReadable(indexMemorySize)))
		memoryUsageInfo.WriteString(fmt.Sprintf(" - original[%s][%s])", task.IndexName, util.ByteToHumanReadable(originalIndexSize)))
	}
	afterIndexMemoryUsageRate = float64(afterIndexMemoryUsage+rssMemory) * 100 / float64(memLimit)
	memoryUsageInfo.WriteString(fmt.Sprintf(" + rssMemory[%s]) / totalMemorySize[%s] = memoryUsageRate[%.2f%%] %s threshold[%d%%]",
		util.ByteToHumanReadable(rssMemory),
		util.ByteToHumanReadable(int64(memLimit)),
		afterIndexMemoryUsageRate,
		func() string {
			if afterIndexMemoryUsageRate > float64(firstTask.MemoryUsageThreshold) {
				return ">"
			}
			return "<="
		}(),
		firstTask.MemoryUsageThreshold))
	log.Info("[%s]index memory check info: %s", requestId, memoryUsageInfo.String())
	resultMessageInfo.MemoryUsageInfo = memoryUsageInfo.String()

	if afterIndexMemoryUsageRate > float64(firstTask.MemoryUsageThreshold) {
		return errors.Errorf("memory space not enough!!! %s", memoryUsageInfo.String())
	}
	return nil
}

func checkDisk(requestId string, isPartialSuccess bool, tasks []*ProcessIndexInfo, resultMessageInfo *ResultMessageInfo) error {
	firstTask := tasks[0]
	// TODO 多个索引累计计算, 无需减去原有索引占用的磁盘空间大小. 因为索引是先下载再解压, 覆盖原有索引
	// 获取父目录
	diskDir := filepath.Dir(firstTask.DbPath)
	diskTotal, diskUsed, err := util.DiskUsage(diskDir)
	if err != nil {
		return errors.WithMessage(err, "get disk usage fail")
	}

	var diskUsageInfo strings.Builder
	diskUsageInfo.WriteString(fmt.Sprintf("(diskUsedSize[%s]", util.ByteToHumanReadable(int64(diskUsed))))

	afterIndexDiskUsage := int64(diskUsed)
	var afterIndexDiskUsageRate float64
	for i, task := range tasks {
		indexTotalBytes := task.buildResult.IndexTotalBytes

		// 磁盘空间不足
		if float64((afterIndexDiskUsage+indexTotalBytes)*100)/float64(diskTotal) > float64(task.DiskUsageThreshold) {
			// 将当前以及其后的任务都设置为不能更新
			for j := i; j < len(tasks); j++ {
				tasks[j].CanUpdate = false
				tasks[j].Reason = "disk space not enough"
			}

			// 首个索引就超过阈值, 需要记录到info中
			if i == 0 || !isPartialSuccess {
				//return errors.Errorf("disk space not enough: %s!!! %s", task.DbPath, diskUsageInfo)
				afterIndexDiskUsage += indexTotalBytes
				afterIndexDiskUsageRate = float64(afterIndexDiskUsage) * 100 / float64(diskTotal)
				diskUsageInfo.WriteString(fmt.Sprintf(" + %s[%s]", task.IndexName, util.ByteToHumanReadable(indexTotalBytes)))
			}
			break
		}

		// 磁盘空间足够
		afterIndexDiskUsage += indexTotalBytes
		afterIndexDiskUsageRate = float64(afterIndexDiskUsage) * 100 / float64(diskTotal)
		diskUsageInfo.WriteString(fmt.Sprintf(" + %s[%s]", task.IndexName, util.ByteToHumanReadable(indexTotalBytes)))
	}

	diskUsageInfo.WriteString(fmt.Sprintf(") / totalDiskSize[%s] = diskUsageRate[%.2f%%] %s threshold[%d%%]",
		util.ByteToHumanReadable(int64(diskTotal)),
		afterIndexDiskUsageRate,
		func() string {
			if afterIndexDiskUsageRate > float64(firstTask.DiskUsageThreshold) {
				return ">"
			}
			return "<="
		}(),
		firstTask.DiskUsageThreshold))
	log.Info("[%s]index disk check info: %s", requestId, diskUsageInfo.String())
	resultMessageInfo.DiskUsageInfo = diskUsageInfo.String()

	// 如果首个索引就超过阈值, 则返回err
	if afterIndexDiskUsageRate > float64(firstTask.DiskUsageThreshold) {
		return errors.Errorf("disk space not enough: %s!!! %s", diskDir, diskUsageInfo.String())
	}
	return nil
}
