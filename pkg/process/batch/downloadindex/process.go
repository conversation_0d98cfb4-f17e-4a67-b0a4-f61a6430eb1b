package downloadindex

import (
	"dip-agent/pkg/core/api"
	"dip-agent/pkg/core/log"
	"dip-agent/pkg/core/process"
	"dip-agent/pkg/dto"
	"dip-agent/pkg/pipeline"
	"dip-agent/pkg/process/download/index"
	"dip-agent/pkg/util"
	"dip-agent/pkg/util/json"
	"github.com/pkg/errors"
)

const (
	TypeName = "batch-download-index"
)

func init() {
	pipeline.Register(api.PROCESSOR, TypeName, makeProcessor)
}

func makeProcessor(info pipeline.Info) api.Component {
	return NewProcessor(info)
}

type ResultMessageInfo struct {
	RequestId         string                    `json:"requestId"`
	IndexDownloadInfo []index.ResultMessageInfo `json:"indexDownloadInfo,omitempty"` // 索引下载信息
}

type ProcessRequest struct {
	Tasks    []*dto.UpdateIndexInfo `json:"tasks" validate:"required,min=1,dive"` // 必填. 任务列表
	IsReboot *bool                  `json:"isReboot,omitempty" default:"false"`   // 是否重启更新
}

type Processor struct {
	process.Abstract
}

func NewProcessor(info pipeline.Info) *Processor {
	return &Processor{
		Abstract: process.Abstract{
			IPipelineName: info.PipelineName,
			TypeName:      TypeName,
		},
	}
}

func (p *Processor) Config() interface{} {
	return nil
}

func (p *Processor) Process(ctx *api.ProcessContext) api.Result {
	pr := &ProcessRequest{}
	err := ctx.Carrier.UnpackToWithJson(pr)
	if err != nil {
		err = errors.WithMessage(err, "invalid param")
		return api.UpdateFail[string](err)
	}
	context, _ := json.MarshalToString(pr)
	log.Info("[%s]batch download index request: %s", ctx.RequestId, context)

	// 执行下载任务
	rmi, err := RunTask(ctx.RequestId, pr)
	if err != nil {
		err = errors.WithMessage(err, "batch download index fail")
		return api.UpdateFail[string](err)
	}

	// 响应内容
	resultMessageContent, _ := json.Marshal(rmi)
	return api.OfSuccessWithMessage[string]("batch download index success", string(resultMessageContent))
}

// RunTask 下载索引
func RunTask(requestId string, pr *ProcessRequest) (*ResultMessageInfo, error) {
	var (
		err error
		rmi = &ResultMessageInfo{
			RequestId:         requestId,
			IndexDownloadInfo: make([]index.ResultMessageInfo, 0),
		}
	)

	// 过滤掉不能更新的任务
	originTasks := pr.Tasks
	validTasks := make([]*dto.UpdateIndexInfo, 0)
	for _, task := range originTasks {
		if !task.CanUpdate {
			log.Warn("[%s]index can not download: %s. reason: %s", requestId, task.IndexName, task.Reason)
			continue
		}
		validTasks = append(validTasks, task)
	}
	log.Info("[%s]download valid tasks count: %v", requestId, len(validTasks))

	// 通用配置
	checkBuildResult := true
	force := true
	isRollback := false
	isVersionEqualCover := true
	moveWithRsync := false

	// 并发执行的任务
	asyncTasks := make([]util.Task[*index.ResultMessageInfo], 0, len(validTasks))

	for _, task := range validTasks {
		ppr := &index.ProcessRequest{
			IndexName:                     task.IndexName,
			IndexVersion:                  task.IndexVersion,
			DbPath:                        task.DbPath,
			GzPath:                        task.GzPath,
			Compress:                      task.Compress,
			CheckBuildResult:              &checkBuildResult,
			Checksum:                      task.Checksum,
			ChecksumFileName:              task.ChecksumFileName,
			Oss:                           task.Oss,
			EngineBuildResultFileName:     task.EngineBuildResultFileName,
			AgentConfigDirName:            task.AgentConfigDirName,
			IsReboot:                      pr.IsReboot,
			Force:                         &force,
			IsVersionEqualCover:           &isVersionEqualCover,
			MemoryInflateFactor:           task.MemoryInflateFactor,
			MemoryUsageThreshold:          task.MemoryUsageThreshold,
			NeedCommit:                    task.NeedCommit,
			MoveWithRsync:                 &moveWithRsync,
			BackupDiskUsageRatio:          0, // 批量更新不备份
			BackupDir:                     task.BackupDir,
			IsRollback:                    &isRollback,
			HotReloadRsyncMemoryThreshold: task.HotReloadRsyncMemoryThreshold,
		}

		asyncTasks = append(asyncTasks, func() (*index.ResultMessageInfo, error) {
			return index.RunTask(requestId, ppr)
		})
	}

	results, err := util.ExecuteTasks(requestId, asyncTasks)
	if err != nil {
		return rmi, err
	}
	for _, result := range results {
		rmi.IndexDownloadInfo = append(rmi.IndexDownloadInfo, *result)
	}

	return rmi, nil
}
