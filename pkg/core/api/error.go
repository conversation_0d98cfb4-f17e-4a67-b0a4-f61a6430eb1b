package api

import (
	"fmt"
	"runtime/debug"
	"strings"
)

const (
	UnknownException       ErrorType = 1000 // 未知错误
	DataException          ErrorType = 2000 // 数据错误
	SystemException        ErrorType = 3000 // 系统错误
	NetworkException       ErrorType = 4000 // 网络错误
	ConfigurationException ErrorType = 5000 // 配置错误
	PermissionException    ErrorType = 6000 // 权限错误
	ParameterException     ErrorType = 7000 // 参数错误

	// ############################## 构建错误 start ##############################
	BuildException ErrorType = 10000 // 构建错误

	BuildException_VectorDimensionNotEqual = 10001 // schema中的向量维度和源数据中维度不一致
	BuildException_VectorIndexTypeNotFloat = 10002 // 向量索引必须是float类型
	BuildException_KeyConflict             = 10003 // key冲突, 请检查源数据
	BuildException_TooFewData              = 10004 // 数据太少，可以把k值调小
	BuildException_InsufficientResources   = 10005 // 构建容器资源不足
	BuildException_InvalidParam            = 10006 // 构建请求参数错误
	// ############################## 构建错误 end ##############################

	// ############################## 更新错误 start ##############################
	UpdateException ErrorType = 20000 // 更新错误

	UpdateException_KeyIdConflict     = 20001 // key id冲突
	UpdateException_WaitIncTimeout    = 20002 // 等待追增量超时
	UpdateException_DumpNotEqual      = 20003 // dump拉取的odps的数据量和最新的odps的分区数量不一致
	UpdateException_OdpsClientTimeout = 20004 // odps client超时
	UpdateException_InvalidParam      = 20005 // 请求参数错误

	UpdateException_BatchUpdatePartialFailed = 24001 // 批量更新部分失败: 2表示更新阶段错误，4表示批量更新，1表示第一个错误码
	// ############################## 更新错误 end ##############################

	// ############################## dump错误 start ##############################
	DumpException ErrorType = 30000 // dump错误

	DumpException_NoData              = 30001 // odps数据为0
	DumpException_InvalidParam        = 30002 // dump请求参数错误
	DumpException_IVT_FIELD_MUST_DOCS = 30003 // 倒排字段必须用docs修饰
	// ############################## dump错误 end ##############################
)

var (
	errorTypeMap = map[ErrorType]string{
		UnknownException:       "未知错误",
		DataException:          "数据错误",
		SystemException:        "系统错误",
		NetworkException:       "网络错误",
		ConfigurationException: "配置错误",
		PermissionException:    "权限错误",
		ParameterException:     "参数错误",

		BuildException:                         "构建错误",
		BuildException_InvalidParam:            "构建请求参数错误",
		BuildException_InsufficientResources:   "构建容器资源不足",
		BuildException_KeyConflict:             "key冲突, 请检查源数据",
		BuildException_TooFewData:              "数据太少，可以把k值调小",
		BuildException_VectorDimensionNotEqual: "schema中的向量维度和源数据中维度不一致",
		BuildException_VectorIndexTypeNotFloat: "向量索引必须是float类型",

		UpdateException:                   "更新错误",
		UpdateException_InvalidParam:      "请求参数错误",
		UpdateException_WaitIncTimeout:    "等待追增量超时",
		UpdateException_DumpNotEqual:      "dump拉取的odps的数据量和最新的odps的分区数量不一致",
		UpdateException_OdpsClientTimeout: "odps client超时",

		DumpException:                     "dump错误",
		DumpException_InvalidParam:        "dump请求参数错误",
		DumpException_NoData:              "odps数据为0",
		DumpException_IVT_FIELD_MUST_DOCS: "倒排字段必须用docs修饰",
	}
)

type ErrorType int

func (et ErrorType) IsUpdateError() bool {
	return et >= UpdateException && et < DumpException
}

func (et ErrorType) IsBuildError() bool {
	return et >= BuildException && et < UpdateException
}

func (et ErrorType) IsDumpError() bool {
	return et >= DumpException
}

func (et ErrorType) String() string {
	if msg, ok := errorTypeMap[et]; ok {
		return msg
	}
	return "未知错误"
}

type ErrorDetails struct {
	ErrorType       ErrorType `json:"errorType"`       // 错误类型枚举
	DetailedMessage string    `json:"detailedMessage"` // 详细错误信息
	Trace           string    `json:"trace"`           // 错误追踪信息，例如堆栈跟踪
}

func (e *ErrorDetails) Error() string {
	var msg strings.Builder
	msg.WriteString(e.ErrorType.String())
	msg.WriteString(": ")
	msg.WriteString(e.DetailedMessage)
	if e.Trace != "" {
		msg.WriteString("\ntrace: ")
		msg.WriteString(e.Trace)
	}
	return msg.String()
}

func (e *ErrorDetails) String() string {
	return e.Error()
}

func FromError(err error) *ErrorDetails {
	return FromErrorWithType(SystemException, err)
}

func FromErrorWithType(et ErrorType, err error) *ErrorDetails {
	if err == nil {
		return nil
	}
	if e, ok := err.(*ErrorDetails); ok {
		return e
	}
	return &ErrorDetails{
		ErrorType:       et,
		DetailedMessage: fmt.Sprintf("%s", err),
		Trace:           fmt.Sprintf("%+v", err),
	}
}

func NewSystemError(msg string) *ErrorDetails {
	return &ErrorDetails{
		ErrorType:       SystemException,
		DetailedMessage: msg,
		Trace:           string(debug.Stack()),
	}
}

func NewDataError(code int, msg string) *ErrorDetails {
	return &ErrorDetails{
		ErrorType:       ErrorType(code),
		DetailedMessage: msg,
		Trace:           string(debug.Stack()),
	}
}

func NewSystemErrorf(format string, args ...interface{}) *ErrorDetails {
	return &ErrorDetails{
		ErrorType:       SystemException,
		DetailedMessage: fmt.Sprintf(format, args...),
		Trace:           string(debug.Stack()),
	}
}

func NewBuildError(msg string) *ErrorDetails {
	return &ErrorDetails{
		ErrorType:       BuildException,
		DetailedMessage: msg,
		Trace:           string(debug.Stack()),
	}
}

func NewBuildErrorWithCode(code int, msg string) *ErrorDetails {
	return &ErrorDetails{
		ErrorType:       ErrorType(code),
		DetailedMessage: msg,
		Trace:           string(debug.Stack()),
	}
}

func NewUpdateError(msg string) *ErrorDetails {
	return &ErrorDetails{
		ErrorType:       UpdateException,
		DetailedMessage: msg,
		Trace:           string(debug.Stack()),
	}
}

func NewUpdateErrorWithCode(code int, msg string) *ErrorDetails {
	return &ErrorDetails{
		ErrorType:       ErrorType(code),
		DetailedMessage: msg,
		Trace:           string(debug.Stack()),
	}
}

func NewDumpError(err error) *ErrorDetails {
	return FromErrorWithType(DumpException, err)
}
